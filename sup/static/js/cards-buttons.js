/**
 * 供货商卡密管理 - 一键删除和导出功能
 */

// 一键删除所有卡密
function deleteAllCards() {
    // 确认对话框
    if (window.ElementUI && window.ElementUI.MessageBox) {
        // 使用Element UI的确认框
        window.ElementUI.MessageBox.confirm(
            '此操作将删除您的所有卡密，且无法恢复！请确认是否继续？',
            '危险操作确认',
            {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }
        ).then(() => {
            executeDeleteAll();
        }).catch(() => {
            // 用户取消
        });
    } else {
        // 降级到原生确认框
        if (confirm('此操作将删除您的所有卡密，且无法恢复！请确认是否继续？')) {
            executeDeleteAll();
        }
    }
}

// 执行删除所有卡密
function executeDeleteAll() {
    // 显示加载状态
    let loadingInstance;
    if (window.ElementUI && window.ElementUI.Loading) {
        loadingInstance = window.ElementUI.Loading.service({
            lock: true,
            text: '正在删除所有卡密...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
    }

    // 发送删除请求
    fetch('/sup/goods/cards.php?act=delAll', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (loadingInstance) {
            loadingInstance.close();
        }
        
        if (data.code === 0) {
            // 删除成功
            if (window.ElementUI && window.ElementUI.Message) {
                window.ElementUI.Message.success(`成功删除 ${data.data.total} 条卡密`);
            } else {
                alert(`成功删除 ${data.data.total} 条卡密`);
            }
            
            // 刷新页面或重新加载数据
            if (window.location.reload) {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            // 删除失败
            if (window.ElementUI && window.ElementUI.Message) {
                window.ElementUI.Message.error(data.msg || '删除失败');
            } else {
                alert(data.msg || '删除失败');
            }
        }
    })
    .catch(error => {
        if (loadingInstance) {
            loadingInstance.close();
        }
        
        console.error('删除失败:', error);
        if (window.ElementUI && window.ElementUI.Message) {
            window.ElementUI.Message.error('网络错误，删除失败');
        } else {
            alert('网络错误，删除失败');
        }
    });
}

// 一键导出所有卡密
function exportAllCards() {
    // 显示加载状态
    let loadingInstance;
    if (window.ElementUI && window.ElementUI.Loading) {
        loadingInstance = window.ElementUI.Loading.service({
            lock: true,
            text: '正在生成导出文件...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });
    }

    // 创建隐藏的下载链接
    const downloadLink = document.createElement('a');
    downloadLink.style.display = 'none';
    downloadLink.href = '/sup/goods/cards.php?act=exportAll';
    downloadLink.download = '';
    
    // 添加到页面并触发下载
    document.body.appendChild(downloadLink);
    downloadLink.click();
    
    // 清理
    setTimeout(() => {
        document.body.removeChild(downloadLink);
        if (loadingInstance) {
            loadingInstance.close();
        }
        
        if (window.ElementUI && window.ElementUI.Message) {
            window.ElementUI.Message.success('卡密导出完成');
        }
    }, 1000);
}

// 添加浮动按钮面板
function addFloatingButtons() {
    // 检查是否已经添加过按钮
    if (document.querySelector('#cards-floating-panel')) {
        return;
    }

    // 创建浮动面板
    const floatingPanel = document.createElement('div');
    floatingPanel.id = 'cards-floating-panel';
    floatingPanel.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 9999;
        background: white;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        padding: 15px;
        display: none;
    `;

    // 创建标题
    const title = document.createElement('div');
    title.innerHTML = '<strong>卡密批量操作</strong>';
    title.style.marginBottom = '10px';
    title.style.fontSize = '14px';
    title.style.color = '#303133';

    // 创建一键删除按钮
    const deleteAllBtn = document.createElement('button');
    deleteAllBtn.className = 'el-button el-button--danger el-button--small';
    deleteAllBtn.innerHTML = '<span>一键删除所有</span>';
    deleteAllBtn.onclick = deleteAllCards;
    deleteAllBtn.style.width = '100%';
    deleteAllBtn.style.marginBottom = '8px';

    // 创建一键导出按钮
    const exportAllBtn = document.createElement('button');
    exportAllBtn.className = 'el-button el-button--success el-button--small';
    exportAllBtn.innerHTML = '<span>一键导出所有</span>';
    exportAllBtn.onclick = exportAllCards;
    exportAllBtn.style.width = '100%';

    // 组装面板
    floatingPanel.appendChild(title);
    floatingPanel.appendChild(deleteAllBtn);
    floatingPanel.appendChild(exportAllBtn);

    // 添加到页面
    document.body.appendChild(floatingPanel);

    console.log('卡密管理浮动按钮面板已添加');
}

// 显示/隐藏浮动按钮面板
function toggleFloatingButtons() {
    const panel = document.querySelector('#cards-floating-panel');
    if (!panel) {
        addFloatingButtons();
        return;
    }

    // 检查当前是否在卡密管理页面
    const currentUrl = window.location.hash || window.location.pathname;
    const isCardsPage = currentUrl.includes('cards') ||
                       document.querySelector('.el-tab-pane[name="kami"].is-active') ||
                       document.querySelector('.el-tabs__item[name="kami"].is-active');

    if (isCardsPage) {
        panel.style.display = 'block';
    } else {
        panel.style.display = 'none';
    }
}

// 添加按钮到页面
function addCardsButtons() {
    addFloatingButtons();
    toggleFloatingButtons();

    // 监听页面变化
    const observer = new MutationObserver(() => {
        toggleFloatingButtons();
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
    });
}

// 页面加载完成后添加按钮
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addCardsButtons);
} else {
    addCardsButtons();
}

// 监听路由变化（Vue Router）
if (window.addEventListener) {
    window.addEventListener('popstate', () => {
        // 路由变化时重新添加按钮
        setTimeout(addCardsButtons, 1000);
    });
}

// 导出函数供全局使用
window.deleteAllCards = deleteAllCards;
window.exportAllCards = exportAllCards;
